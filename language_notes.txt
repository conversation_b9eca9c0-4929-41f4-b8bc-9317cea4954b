Scratch Notes

* Unsugared variable creation is done via an extra keyword in the argument list.  In the following example, y and z pre-exist, but x is introduced
  with the .var keyword.  If a bounding type is desired, this can be supplied as well; type names are specified before variables.  If the variable
  introduced by the var command is not writeable, then its value cannot be changed after its initial assignment.  If the introduced variable is 
  writeable then it can be re-assigned further.
    doSomething: .var x, y, z
    doSomething: .var Numeric x, y, z
* Expressions are syntactic sugar, and can be used in command invocation.  
    doSomething: x, (doAnotherThing: y), z
* Assignment can also be done via syntactic sugar via the <- operator.
    .var x <- doSomething: x, y
* Values may be assigned similarly
    .var x <- 5.3
* Commands are first-class values, and can be assigned 
    .var x <- doSomething
* If the right-hand side of an assignment is a command with no input arguments, and one wishes to assign the result, then the rhs must be within
  parentheses
    .var x <- (doSomething)    ; no colon in the command invocation, as there are no regular parameters being passed
* When defining a command with two or more output parameters, one may designate which output parameter is used as an expression result.
    .cmd do: 'x, 'y, z -> 'y
    'r <- (do: 'x, y)  ; 'r gets the value that the do command assigns to its 'y argument
* In addition to regular parameters, a command may be defined to accept a context argument.  Instead of a semicolon, context arguments are
  separated by a / character.  Context arguments are supplied automatically as a syntactic sugar: so long as there is one and only one 
  variable of the type indicated as the context parameter type, then the user need not specify it.  If there is more than one variable of
  the same type as the context parameter, then this must be specified in the invocation
    .cmd doSomething: T1 a, T2 b / T3 c  ; c is a context paramter
    doSomething: x, y                    ; succeeds if there is one and only one variable of type T3 in scope
* The last parameter type that can be passed to a command is a block of commands to be treated as a command.  These are separated from other
  arguments by a ~ character.  All content indented from the ~ is collected as the block to be passed.  Unlike regular arguments, each block
  argument must be proceeded by a ~ even if it's the first argument to be passed.  The type description for block arguments will be described
  further below.
    .cmd someCommand : Number arg1, :<> b1, :<> b2 / context  ; a command which accepts a regular argument, two block arguments, & a context argument
    .someCommand: 5  ; if there was another regular argument between arg1 and b1, it would be here, separated by a comma
       ~ doSomething: a, b
         doSomethingElse: x, y
         doALastThing
       ~ doSomething: x, y
         doSomethingElse: a,   
       ; the context argument is usually matched automatically and doesn't need to be supplied
* The body of a command is the remainder of the command that is indented from the command declaration, after an equals sign
    .cmd addFive: 'r, x = add: 'r, x, 5
    .cmd addSix 'r, x
      = add: 'r, x, 6

 


---------------------------
.cmd c: []Int v, … – command taking a vector of Int
.cmd c: []@Int v – command taking a vector of pointers to Int
.cmd c: @[]Int v – command taking a pointer to a vector of Int

.cmd c: T1 'a, T2 'b -> 'a = …

– .var to introduce a variable
.var id 
– Using a type bound; applies to initialization and re-initialization
.var T id

– Generic type with constrait
.cmd f: Radians 'r, (T:Number) b, T c 
    = add: 'r, b, c  – smaller, doesn't mess up block


.subtype Polar: Number;
.extend Inches : Double;

.cmd f : []T 'a, @T b, U 'c  = ….
the type of f is :<[]T', @T, U'>

.cmd TargetType 'x: SourceType a = …
.cmd Long 's: Int t .intrinsic
.cmd ?Int 'i: Long l

.stop (condition constructor)
– exit without a fail
.exit
         


idea: enumeration as restricted set a la XML Schema, implicitly as per c if a type isn't specified
.enum fish: Sockeye, Salmon
.enum Text fish: 
    Sockeye = "Sockeye",
    Salmon = "Salmon"
.enum Double angle : Zero = 0.0, Quarter = 90.0


.cmd f = add…. 
         addMore:...
       | ? 'a = .subtract: … 
         fish 

.class foo =
    .cmd m : x, y 
    .cmd y
    .cmd z

.var .object : A a, B b
     fish;

<< streaming write operator, returns lhs
>> streaming read operator, returns lhs
<- assignment
-= decrement
+= increment
|=, &=, ^= bitwise operators

record[field]
record[field][subindex]
vector[index]
object[command] 
record[field,] – bytes starting at "field"
record[start,finish] – bytes from start up to but not including finish
record[,finish] - bytes up to finish
record[field:length] – length bytes starting at field
record[field:] – all bytes starting at field
record[:length] – the first length bytes
record[:field] - error
record[start:length] – length bytes starting at start
 
.cmd openFile: String filename = .fail – compile-time error
.cmd ?openFile: String filename = .fail – ok
– command types
– :<...> command that doesn't fail
– ?<...> command that can fail

– perhaps use :: for command invocation requiring dynamic dispatch?  it'd involve more mental
– bookkeeping, but would retain the "no surprises" ethos
Object obj :: method : args = ..
Struct s  :: method : args = …

– static dispatch of the method (avoids overheads in loops)
m <- obj :: method
m : …

– mark the object if you don't want a const method
Object 'obj :: method : . . . 

– what about multiple-dispatch?
ObjType i, ObjType j:: method : x, y, z = …

– destructuring dispatch?
(ObjectType a,b) obj :: method Int 'r = 'r <- (a + b)

– nominal destructuring dispatch… ignores other contents
{Int x, Int y}, Int c :: method Int 'r = 'r <- (c*(a + b))



