#ifndef TOKEN_H
#define TOKEN_H

#include <cstddef>
#include <string>

namespace basis {
    enum class TokenType {
        _NOTHING,
        // literals
        DECIMAL,
        H<PERSON><PERSON>MBER,
        <PERSON>UMBER,
        STRING,
        // identifiers
        IDENTIFIER,
        // reserved words
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        D<PERSON>AIN,
        ENU<PERSON>RATION,
        INTRINSIC,
        OBJECT,
        <PERSON><PERSON><PERSON><PERSON>,
        // punctuation
        AMBANG,
        AMPERSAND,
        AMPHORA,
        ASSIGN,
        ASTERISK,
        BANG,
        BANGLA<PERSON>LE,
        CARAT,
        COMM<PERSON>,
        <PERSON>L<PERSON>,
        <PERSON>LA<PERSON><PERSON>,
        DC<PERSON>ON,
        EQUALS,
        LA<PERSON>LE,
        LBRACE,
        LBR<PERSON>KET,
        LPAREN,
        MINUS,
        PLUS,
        QCOLON,
        QLANGLE,
        QMARK,
        RANGLE,
        RBRACE,
        RBRACKET,
        RPAREN,
        SLASH,
    };

    class Token {
    public:
        TokenType type;
        std::string text;
        size_t lineNumber;
        size_t columnNumber;
        Token* bound;
        Token() : type(TokenType::_NOTHING), lineNumber(0), columnNumber(0), bound(nullptr) {}
    };

    bool operator==(const Token& lhs, const Token& rhs);
}



#endif //TOKEN_H
