#ifndef PARSER2_H
#define PARSER2_H

#include <list>
#include <vector>
#include <memory>
#include <type_traits>

#include "ParseObject.h"
#include "Token.h"

namespace basis {

    using itToken = std::list<Token>::const_iterator;

    // Template metaprogramming parser using compile-time dispatch
    template<typename ParseFnType>
    class Parser2 {
    public:
        explicit Parser2(const std::list<Token>& tokens) : tokens(tokens) {}
        
        template<typename... Args>
        bool parse(Args&&... args) {
            itToken start = tokens.cbegin();
            spParseTree* pTree = &parseTree;
            return ParseFnType::parse(&pTree, &start, nullptr, std::forward<Args>(args)...);
        }
        
        bool atLimit(itToken* pIter, const Token* pLimit) const {
            return (*pIter) == tokens.cend() || (pLimit != nullptr && &(**pIter) == pLimit);
        }
        
        spParseTree parseTree;
        
    private:
        const std::list<Token>& tokens;
    };

    // Template-based parse function types
    template<TokenType Type>
    struct Discard {
        template<typename ParserType>
        static bool parse(spParseTree** _unused, itToken* pIter, const Token* pLimit, ParserType& parser) {
            if (parser.atLimit(pIter, pLimit)) return false;
            if ((*pIter)->type == Type) {
                ++(*pIter);
                return true;
            }
            return false;
        }
    };

    template<Production Prod, TokenType Type>
    struct Match {
        template<typename ParserType>
        static bool parse(spParseTree** dpspResult, itToken* pIter, const Token* pLimit, ParserType& parser) {
            if (parser.atLimit(pIter, pLimit)) return false;
            if ((*pIter)->type == Type) {
                **dpspResult = std::make_shared<ParseTree>(Prod, &(**pIter));
                ++(*pIter);
                *dpspResult = &((**dpspResult)->spNext);
                return true;
            }
            return false;
        }
    };

    template<typename ParseFnType>
    struct Maybe {
        template<typename ParserType, typename... Args>
        static bool parse(spParseTree** dpspResult, itToken* pIter, const Token* pLimit, ParserType& parser, Args&&... args) {
            return ParseFnType::parse(dpspResult, pIter, pLimit, parser, std::forward<Args>(args)...) || true;
        }
    };

    template<typename... ParseFnTypes>
    struct Any {
        template<typename ParserType, typename... Args>
        static bool parse(spParseTree** dpspResult, itToken* pIter, const Token* pLimit, ParserType& parser, Args&&... args) {
            itToken start = *pIter;
            return try_parse<ParseFnTypes...>(dpspResult, pIter, pLimit, start, parser, std::forward<Args>(args)...);
        }
        
    private:
        template<typename FirstFn, typename... RestFns, typename ParserType, typename... Args>
        static bool try_parse(spParseTree** dpspResult, itToken* pIter, const Token* pLimit, 
                             const itToken& start, ParserType& parser, Args&&... args) {
            if (FirstFn::parse(dpspResult, pIter, pLimit, parser, std::forward<Args>(args)...)) {
                return true;
            }
            *pIter = start;
            if constexpr (sizeof...(RestFns) > 0) {
                return try_parse<RestFns...>(dpspResult, pIter, pLimit, start, parser, std::forward<Args>(args)...);
            }
            return false;
        }
    };

    template<typename... ParseFnTypes>
    struct All {
        template<typename ParserType, typename... Args>
        static bool parse(spParseTree** dpspResult, itToken* pIter, const Token* pLimit, ParserType& parser, Args&&... args) {
            spParseTree* next = *dpspResult;
            return parse_all<ParseFnTypes...>(&next, pIter, pLimit, parser, std::forward<Args>(args)...) && 
                   (*dpspResult = next, true);
        }
        
    private:
        template<typename FirstFn, typename... RestFns, typename ParserType, typename... Args>
        static bool parse_all(spParseTree** next, itToken* pIter, const Token* pLimit, 
                             ParserType& parser, Args&&... args) {
            if (FirstFn::parse(next, pIter, pLimit, parser, std::forward<Args>(args)...)) {
                if (*next) next = &((*next)->spNext);
                if constexpr (sizeof...(RestFns) > 0) {
                    return parse_all<RestFns...>(next, pIter, pLimit, parser, std::forward<Args>(args)...);
                }
                return true;
            }
            return false;
        }
    };

    template<typename ParseFnType>
    struct OneOrMore {
        template<typename ParserType, typename... Args>
        static bool parse(spParseTree** dpspResult, itToken* pIter, const Token* pLimit, ParserType& parser, Args&&... args) {
            if (!ParseFnType::parse(dpspResult, pIter, pLimit, parser, std::forward<Args>(args)...)) {
                return false;
            }
            spParseTree* next = *dpspResult;
            if (*next) next = &((*next)->spNext);
            while (ParseFnType::parse(&next, pIter, pLimit, parser, std::forward<Args>(args)...)) {
                if (*next) next = &((*next)->spNext);
            }
            *dpspResult = next;
            return true;
        }
    };

    template<typename ParseFnType>
    struct Bound {
        template<typename ParserType, typename... Args>
        static bool parse(spParseTree** dpspResult, itToken* pIter, const Token* pLimit, ParserType& parser, Args&&... args) {
            return ParseFnType::parse(dpspResult, pIter, (*pIter)->bound, parser, std::forward<Args>(args)...);
        }
    };

    template<Production Prod, typename ParseFnType>
    struct Group {
        template<typename ParserType, typename... Args>
        static bool parse(spParseTree** dpspResult, itToken* pIter, const Token* pLimit, ParserType& parser, Args&&... args) {
            spParseTree* target = *dpspResult;
            (*target) = std::make_shared<ParseTree>(Prod);
            spParseTree* down = &(*target)->spDown;
            if (ParseFnType::parse(&down, pIter, pLimit, parser, std::forward<Args>(args)...)) {
                return true;
            }
            target->reset();
            return false;
        }
    };

    // Helper type aliases for easier usage
    template<TokenType Type>
    using discard = Discard<Type>;
    
    template<Production Prod, TokenType Type>
    using match = Match<Prod, Type>;
    
    template<typename ParseFnType>
    using maybe = Maybe<ParseFnType>;
    
    template<typename... ParseFnTypes>
    using any = Any<ParseFnTypes...>;
    
    template<typename... ParseFnTypes>
    using all = All<ParseFnTypes...>;
    
    template<typename ParseFnType>
    using oneOrMore = OneOrMore<ParseFnType>;
    
    template<typename ParseFnType>
    using bound = Bound<ParseFnType>;
    
    template<Production Prod, typename ParseFnType>
    using group = Group<Prod, ParseFnType>;
}

#endif // PARSER2_H
